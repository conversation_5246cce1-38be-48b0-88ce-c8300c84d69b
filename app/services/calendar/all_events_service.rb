# frozen_string_literal: true

class Calendar::AllEventsService
  def initialize(student_canvas_id, calendar_params)
    @student_canvas_id = student_canvas_id
    @calendar_params = calendar_params
  end

  def call
    normalized_events = []

    # Call EventsService with different types
    normalized_events.concat(fetch_and_normalize_events)

    # Call PlannerNotesService (unless undated)
    normalized_events.concat(fetch_and_normalize_planner_notes)

    # Call ItemsService with different filters
    normalized_events.concat(fetch_and_normalize_items)

    normalized_events
  end

  private

  attr_reader :student_canvas_id, :calendar_params

  def fetch_and_normalize_events
    events = []

    # Enhanced logic for event types
    event_types = %w[assignment sub_assignment]
    # Include user events as well if undated filter is not applied
    event_types.push(nil) unless undated_filter?

    event_types.each do |event_type|
      service_params = calendar_params.merge(type: event_type)
      service = Calendar::EventsService.new(student_canvas_id, service_params)
      calendar_events = service.call

      calendar_events.each do |event|
        events << normalize_event(event)
      end
    end

    events
  end

  def fetch_and_normalize_planner_notes
    return [] if undated_filter?

    service = Calendar::PlannerNotesService.new(student_canvas_id, calendar_params)
    planner_notes = service.call

    planner_notes.map { |note| normalize_planner_note(note) }
  end

  def fetch_and_normalize_items
    items = []

    # Enhanced logic for item filters
    item_filters = %w[ungraded_todo_items all_ungraded_todo_items]
    item_filters.each do |filter|
      service_params = calendar_params.merge(filter: filter)
      service_params[:context_codes] ||= []

      if filter == 'all_ungraded_todo_items'
        service_params[:context_codes] = service_params[:context_codes].select { |code| code.start_with?('user_') }
      elsif filter == 'ungraded_todo_items'
        service_params[:context_codes] = service_params[:context_codes].select { |code| code.start_with?('course_') }
      end
      next if service_params[:context_codes].blank?

      service = Calendar::ItemsService.new(student_canvas_id, service_params)
      service_items = service.call

      service_items.each do |item|
        items << normalize_item(item)
      end
    end

    items
  end

  def undated_filter?
    [true, 'true', 1, '1'].include?(calendar_params[:undated])
  end

  def normalize_event(event)
    type = if event[:type] == 'assignment'
             if event[:assignment].key?(:discussion_topic)
               'discussion_topic'
             elsif event[:assignment].key?(:quiz_id)
               'quiz'
             else
               'assignment'
             end
           else
             'event'
           end

    {
      context_code: event[:context_code],
      context_name: event[:context_name],
      all_day: event[:all_day],
      title: event[:title],
      completed: event.dig(:assignment, :has_submitted_submissions),
      description: event[:description],
      workflow_state: event[:workflow_state],
      date: event[:start_at],
      start_at: event[:start_at],
      end_at: event[:end_at],
      due_at: event.dig(:assignment, :due_at),
      location_name: event[:location_name],
      location_address: event[:location_address],
      type: type,
      html_url: event[:html_url],
      color_key: event[:context_code]
    }
  end

  def normalize_planner_note(note)
    context_code = note[:course_id] ? "course_#{note[:course_id]}" : "user_#{note[:user_id]}"

    {
      id: note[:id],
      user_id: note[:user_id],
      course_id: note[:course_id],
      start_at: note[:todo_date],
      date: note[:todo_date],
      title: note[:title],
      description: note[:details],
      workflow_state: note[:workflow_state],
      context_code: context_code,
      type: 'planner_note',
      color_key: context_code
    }
  end

  def normalize_item(item)
    plannable = item[:plannable] || {}

    # Determine context_code based on context_type
    context_code = item[:context_type] == 'Course' ? "course_#{item[:course_id]}" : "user_#{student_canvas_id}"

    {
      context_type: item[:context_type],
      course_id: item[:course_id],
      context_name: item[:context_name],
      context_code: context_code,
      date: item[:plannable_date] || plannable[:due_at],
      html_url: item[:html_url],
      id: item[:plannable_id],
      title: plannable[:title],
      description: plannable[:description] || plannable[:details],
      user_id: plannable[:user_id],
      location_address: plannable[:location_address],
      start_at: plannable[:start_at] || item[:plannable_date] || plannable[:due_at],
      end_at: plannable[:end_at],
      todo_date: plannable[:due_at],
      type: item[:plannable_type],
      color_key: context_code
    }
  end
end
