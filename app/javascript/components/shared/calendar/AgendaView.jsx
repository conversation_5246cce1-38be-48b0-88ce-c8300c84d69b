import React, { useMemo, useState } from 'react'
import { View } from '@instructure/ui-view'
import { Text } from '@instructure/ui-text'
import { TruncateText } from '@instructure/ui-truncate-text'
import { Flex } from '@instructure/ui-flex'
import { Popover } from '@instructure/ui-popover'
import { CloseButton } from '@instructure/ui-buttons'
import {
  IconAssignmentLine,
  IconQuizLine,
  IconCalendarMonthLine,
  IconDiscussionLine,
  IconNoteLine,
  IconAnnouncementLine
} from '@instructure/ui-icons'

const ICONS = {
  assignment: IconAssignmentLine,
  discussion_topic: IconDiscussionLine,
  quiz: IconQuizLine,
  announcement: IconAnnouncementLine,
  event: IconCalendarMonthLine,
  planner_note: IconNoteLine
}

const AgendaView = ({
  events = [],
  colorMap = {},
  loading = false,
  selectedDate = new Date()
}) => {
  const [openPopoverKey, setOpenPopoverKey] = useState(false)
  // Sort all events by date and filter from selectedDate forward
  const agendaEvents = useMemo(() => {
    const startDate = new Date(selectedDate)
    startDate.setHours(0, 0, 0, 0) // Start of selected day

    return events
      .filter((event) => {
        const eventDate = event.date
          ? new Date(event.date)
          : event.start_at
            ? new Date(event.start_at)
            : null

        if (eventDate === null) return false // Filter out events without dates

        // Only show events from selectedDate forward
        return eventDate >= startDate
      })
      .sort((a, b) => {
        const dateA = a.date
          ? new Date(a.date)
          : a.start_at
            ? new Date(a.start_at)
            : new Date(0)
        const dateB = b.date
          ? new Date(b.date)
          : b.start_at
            ? new Date(b.start_at)
            : new Date(0)
        return dateA - dateB
      })
  }, [events, selectedDate])

  // Group events by date
  const eventsByDate = useMemo(() => {
    const grouped = new Map()
    agendaEvents.forEach((event) => {
      let eventDate = null

      if (event.date) {
        // Convert UTC date to local date
        const utcDate = new Date(event.date)
        eventDate = utcDate.toLocaleDateString('en-CA') // YYYY-MM-DD format in local timezone
      } else if (event.start_at) {
        // Convert UTC timestamp to local date
        const utcDate = new Date(event.start_at)
        eventDate = utcDate.toLocaleDateString('en-CA') // YYYY-MM-DD format in local timezone
      }

      if (eventDate) {
        if (!grouped.has(eventDate)) {
          grouped.set(eventDate, [])
        }
        grouped.get(eventDate).push(event)
      }
    })
    return grouped
  }, [agendaEvents])

  const formatDate = (dateStr) => {
    const date = new Date(dateStr)
    const today = new Date()

    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      day: 'numeric',
      month: 'short',
      year: date.getFullYear() !== today.getFullYear() ? 'numeric' : undefined
    })
  }

  const formatTime = (event) => {
    if (event.all_day) return ''

    const timeOptions = { hour: 'numeric', minute: '2-digit', hour12: false }

    if (event.due_at) {
      const dueTime = new Date(event.due_at).toLocaleTimeString(
        'en-US',
        timeOptions
      )
      return `Due: ${dueTime}`
    }

    return new Date(event.start_at).toLocaleTimeString('en-US', timeOptions)
  }

  if (loading) {
    return (
      <View as="div" padding="medium" textAlign="center">
        <Text color="secondary">Loading events...</Text>
      </View>
    )
  }

  if (agendaEvents.length === 0) {
    return (
      <View as="div" padding="medium" textAlign="center">
        <Text color="secondary">No events found.</Text>
      </View>
    )
  }

  return (
    <View as="div">
      {Array.from(eventsByDate.entries()).map(([date, dayEvents]) => {
        const today = new Date()
        const eventDate = new Date(date)
        const isToday =
          eventDate.getDate() === today.getDate() &&
          eventDate.getMonth() === today.getMonth() &&
          eventDate.getFullYear() === today.getFullYear()
        const gridBackground = isToday ? 'secondary' : 'primary'

        return (
          <View key={date} as="div" margin="medium none">
            <View
              as="div"
              padding="xx-small none"
              borderWidth="none none small none"
              borderColor="primary"
              background={gridBackground}
            >
              <Text size="small" weight="weightRegular" variant="contentSmall">
                {formatDate(date)}
              </Text>
            </View>

            {dayEvents.map((event, idx) => {
              const eventColor = colorMap[event.context_code] || '#2b7abc'
              const EventIcon = ICONS[event.type] || IconCalendarMonthLine

              return (
                <Popover
                  renderTrigger={
                    <View
                      padding="small none none none"
                      key={`${event.id || idx}`}
                      as="div"
                      cursor="pointer"
                    >
                      <Flex alignItems="start" gap="small">
                        <Flex.Item width="1rem">
                          <EventIcon
                            style={{
                              color: eventColor
                            }}
                          />
                          &nbsp;
                        </Flex.Item>
                        <Flex.Item width="7rem">
                          <Text weight="light">{formatTime(event)}</Text>
                        </Flex.Item>
                        <Flex.Item>
                          <span
                            style={{
                              color: eventColor,
                              fontWeight: '400'
                            }}
                          >
                            <TruncateText>{event.title}</TruncateText>
                          </span>
                        </Flex.Item>
                      </Flex>
                    </View>
                  }
                  on="click"
                  screenReaderLabel="Event details"
                  shouldContainFocus
                  shouldReturnFocus
                  shouldCloseOnDocumentClick
                  offsetY="16px"
                  onShowContent={() => setOpenPopoverKey(true)}
                  onHideContent={() => setOpenPopoverKey(false)}
                >
                  <View padding="small" display="block" as="div">
                    <Flex
                      direction="column"
                      margin="none none small"
                      gap="small"
                    >
                      <Flex.Item key={`${event.id || idx}`}>
                        <CloseButton
                          placement="end"
                          onClick={() => setOpenPopoverKey(false)}
                          screenReaderLabel="Close"
                        />
                      </Flex.Item>
                      <Flex.Item>
                        {' '}
                        <Text size="descriptionPage">{event.title}</Text>
                      </Flex.Item>
                      <hr
                        style={{
                          border: 'none',
                          borderTop: '1px solid #e6e7e9',
                          margin: '8px 0 0 0'
                        }}
                      />

                      <Flex.Item>
                        Calendar: <Text>{event.context_name}</Text>{' '}
                      </Flex.Item>
                      {event.description && (
                        <Flex.Item>
                          Details: <Text>{event.description}</Text>
                        </Flex.Item>
                      )}
                      {event.location_name && (
                        <Flex.Item>
                          Location: <Text>{event.location_name}</Text>
                        </Flex.Item>
                      )}
                    </Flex>
                  </View>
                </Popover>
              )
            })}
          </View>
        )
      })}
    </View>
  )
}

export default AgendaView
