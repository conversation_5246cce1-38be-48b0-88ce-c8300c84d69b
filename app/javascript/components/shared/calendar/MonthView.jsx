import React, {
  useEffect,
  useState,
  useMemo,
  useCallback,
  memo,
  forwardRef
} from 'react'
import { Grid } from '@instructure/ui-grid'
import { View } from '@instructure/ui-view'
import { Text } from '@instructure/ui-text'
import { TruncateText } from '@instructure/ui-truncate-text'
import { Flex } from '@instructure/ui-flex'
import { Popover } from '@instructure/ui-popover'
import { CloseButton } from '@instructure/ui-buttons'
import {
  IconAssignmentLine,
  IconQuizLine,
  IconCalendarMonthLine,
  IconDiscussionLine,
  IconNoteLine,
  IconAnnouncementLine
} from '@instructure/ui-icons'
import { Link } from '@instructure/ui'

const ICONS = {
  assignment: IconAssignmentLine,
  discussion_topic: IconDiscussionLine,
  quiz: IconQuizLine,
  announcement: IconAnnouncementLine,
  event: IconCalendarMonthLine,
  planner_note: IconNoteLine
}

const MonthView = memo(
  ({ daysOfWeek, meta, today, colorMap, events, loading }) => {
    // Memoize calendar cells calculation
    const cells = useMemo(() => {
      const cells = []
      const pad = (n) => String(n).padStart(2, '0')
      // Previous month days
      for (let i = 0; i < meta.startDayIdx; i++) {
        let year = meta.month === 0 ? meta.year - 1 : meta.year
        let month = meta.month === 0 ? 11 : meta.month - 1
        let day = meta.prevMonthLastDay - meta.startDayIdx + i + 1
        let fullDate = `${year}-${pad(month + 1)}-${pad(day)}`
        cells.push({ day, type: 'prev', fullDate })
      }
      // Current month days
      for (let d = 1; d <= meta.daysInMonth; d++) {
        let fullDate = `${meta.year}-${pad(meta.month + 1)}-${pad(d)}`
        cells.push({ day: d, type: 'current', fullDate })
      }
      // Next month days
      let nextDay = 1
      let nextMonth = meta.month === 11 ? 0 : meta.month + 1
      let nextYear = meta.month === 11 ? meta.year + 1 : meta.year
      // Fill up to next multiple of 7 (35 or 42)
      const totalCells =
        cells.length % 7 === 0
          ? cells.length
          : cells.length + (7 - (cells.length % 7))

      while (cells.length < totalCells) {
        let fullDate = `${nextYear}-${pad(nextMonth + 1)}-${pad(nextDay)}`
        cells.push({ day: nextDay++, type: 'next', fullDate })
      }
      return cells
    }, [
      meta.year,
      meta.month,
      meta.startDayIdx,
      meta.daysInMonth,
      meta.prevMonthLastDay
    ])

    // Memoize date range calculation
    const { startDate, endDate } = useMemo(() => {
      const pad = (n) => String(n).padStart(2, '0')
      let startYear, startMonth
      if (cells[0].type === 'prev') {
        startYear = meta.month === 0 ? meta.year - 1 : meta.year
        startMonth = meta.month === 0 ? 11 : meta.month - 1
      } else {
        startYear = meta.year
        startMonth = meta.month
      }
      let endYear, endMonth
      if (cells[cells.length - 1].type === 'next') {
        endYear = meta.month === 11 ? meta.year + 1 : meta.year
        endMonth = meta.month === 11 ? 0 : meta.month + 1
      } else {
        endYear = meta.year
        endMonth = meta.month
      }
      const startDate = `${startYear}-${pad(startMonth + 1)}-${pad(cells[0].day)}`
      const endDate = `${endYear}-${pad(endMonth + 1)}-${pad(cells[cells.length - 1].day)}`
      return { startDate, endDate }
    }, [cells, meta.year, meta.month])

    // Control which popover is open (by event key); null means none open
    const [openPopoverKey, setOpenPopoverKey] = useState(null)

    // Memoize event key generation
    const getEventKey = useCallback((event, index) => {
      return (
        event.id ||
        event.html_url ||
        `${event.context_code || 'ctx'}:${event.type || 'event'}:${event.title || 'untitled'}:${index}`
      )
    }, [])

    // Pre-compute events by date for faster lookup
    const eventsByDate = useMemo(() => {
      const map = new Map()
      events.forEach((event) => {
        let eventDate = null

        if (event.date) {
          // Convert UTC date to local date
          const utcDate = new Date(event.date)
          eventDate = utcDate.toLocaleDateString('en-CA') // YYYY-MM-DD format in local timezone
        } else if (event.start_at) {
          // Convert UTC timestamp to local date
          const utcDate = new Date(event.start_at)
          eventDate = utcDate.toLocaleDateString('en-CA') // YYYY-MM-DD format in local timezone
        }

        if (eventDate) {
          if (!map.has(eventDate)) {
            map.set(eventDate, [])
          }
          map.get(eventDate).push(event)
        }
      })
      return map
    }, [events])

    const utcToLocalHHMM = (utcString) => {
      if (!utcString) return null

      const date = new Date(utcString) // parse UTC ISO string
      const hours = String(date.getHours()).padStart(2, '0') // local hours
      const minutes = String(date.getMinutes()).padStart(2, '0') // local minutes
      return `${hours}:${minutes}`
    }

    // Memoized event item component with ref forwarding; spreads trigger props from Popover
    const EventItem = memo(
      forwardRef(({ event, colorMap, ...triggerProps }, ref) => {
        const eventColor = colorMap[event.context_code] || '#2b7abc'
        const startTime = utcToLocalHHMM(event.start_at)
        const EventIcon = ICONS[event.type] || IconCalendarMonthLine

        return (
          <View
            ref={ref}
            {...triggerProps}
            as="div"
            borderColor="brand"
            borderWidth="small"
            borderStyle="solid"
            borderRadius="medium"
            padding="xx-small"
            margin="xxx-small"
            cursor="pointer"
            themeOverride={{ borderColorBrand: eventColor }}
          >
            <Flex>
              <Flex.Item>
                <EventIcon
                  size="small"
                  style={{
                    color: eventColor
                  }}
                  themeOverride={{ sizeSmall: '1rem' }}
                />
                &nbsp;
              </Flex.Item>
              {!event.all_day && startTime && (
                <Flex.Item>
                  <Text
                    color="brand"
                    variant="contentSmall"
                    themeOverride={{ brandColor: eventColor }}
                  >
                    {startTime}&nbsp;
                  </Text>
                </Flex.Item>
              )}
              <Flex.Item>
                <span
                  style={{
                    display: 'inline-block',
                    maxWidth: '100%',
                    width: '100%'
                  }}
                >
                  <Text
                    color="brand"
                    variant="contentSmall"
                    themeOverride={{ brandColor: eventColor }}
                  >
                    <span
                      style={{
                        textDecoration: event.completed
                          ? 'line-through'
                          : 'none'
                      }}
                    >
                      {event.title || ''}
                    </span>
                  </Text>
                </span>
              </Flex.Item>
            </Flex>
          </View>
        )
      })
    )

    const renderEventItem = useCallback(
      (event, key) => {
        const startTime = event.start_at ? new Date(event.start_at) : null
        const endTime = event.end_at ? new Date(event.end_at) : null

        const due_at = event.due_at ? new Date(event.due_at) : null
        const day = due_at ? due_at.getDate() : null
        const month = due_at
          ? due_at.toLocaleString('en-US', { month: 'short' })
          : null
        const due_time = due_at
          ? due_at.toLocaleTimeString('en-US', {
              hour: 'numeric',
              minute: '2-digit',
              hour12: false
            })
          : null
        const formatted_due_date = due_time
          ? `${day} ${month} at ${due_time}`
          : null

        const eventDate = new Date(startTime).getDate()
        const eventMonth = new Date(startTime).toLocaleString('en-US', {
          month: 'short'
        })

        const timeDisplay = due_at
          ? due_at.toLocaleTimeString('en-US', {
              hour: 'numeric',
              minute: '2-digit',
              hour12: false
            })
          : `${startTime.toLocaleTimeString('en-US', {
              hour: 'numeric',
              minute: '2-digit',
              hour12: false
            })}${
              endTime
                ? ` - ${endTime.toLocaleTimeString('en-US', {
                    hour: 'numeric',
                    minute: '2-digit',
                    hour12: false
                  })}`
                : ''
            }`

        return (
          <Popover
            renderTrigger={(triggerProps) => (
              <EventItem
                {...triggerProps}
                key={key}
                event={event}
                colorMap={colorMap}
                id="event-view"
              />
            )}
            on="click"
            screenReaderLabel="Event details"
            shouldContainFocus
            shouldReturnFocus
            shouldCloseOnDocumentClick
            offsetY="16px"
            isShowingContent={openPopoverKey === key}
            onShowContent={() => setOpenPopoverKey(key)}
            onHideContent={() => setOpenPopoverKey(null)}
          >
            <View padding="small" display="block" as="div">
              <Flex direction="column" margin="none none small" gap="small">
                <Flex.Item key={key}>
                  <CloseButton
                    placement="end"
                    onClick={() => setOpenPopoverKey(null)}
                    screenReaderLabel="Close"
                  />
                </Flex.Item>
                <Flex.Item>
                  {' '}
                  <Text size="descriptionPage">{event.title}</Text>
                </Flex.Item>
                <hr
                  style={{
                    border: 'none',
                    borderTop: '1px solid #e6e7e9',
                    margin: '8px 0 0 0'
                  }}
                />
                <Flex.Item>
                  {`${eventDate} ${eventMonth}, ${timeDisplay}`}
                </Flex.Item>
                {formatted_due_date && (
                  <Flex.Item>
                    {' '}
                    Due: <Text>{formatted_due_date}</Text>
                  </Flex.Item>
                )}
                <Flex.Item>
                  Calendar: <Text>{event.context_name}</Text>{' '}
                </Flex.Item>
                {event.description && (
                  <Flex.Item>
                    Details: <Text>{event.description}</Text>
                  </Flex.Item>
                )}
                {event.location_name && (
                  <Flex.Item>
                    Location: <Text>{event.location_name}</Text>
                  </Flex.Item>
                )}
              </Flex>
            </View>
          </Popover>
        )
      },
      [colorMap, openPopoverKey]
    )

    // Memoized day cell component
    const DayCell = memo(
      ({ cell, today, meta, eventsForCell, renderEventItem, getEventKey }) => {
        const isToday =
          cell.type === 'current' &&
          cell.day === today.getDate() &&
          meta.month === today.getMonth() &&
          meta.year === today.getFullYear()
        const gridBackground =
          isToday && cell.type === 'current' ? 'secondary' : 'primary'

        return (
          <View
            as="div"
            display="block"
            borderRadius={cell.type === 'current' ? 'medium' : 'none'}
            background={gridBackground}
            minHeight="10rem"
            padding="none"
            textAlign="end"
          >
            <Flex direction="column">
              <Flex.Item>
                <Text
                  color={cell.type === 'current' ? undefined : 'secondary'}
                  themeOverride={{ secondaryColor: '#898e93' }}
                >
                  {cell.day}
                </Text>
              </Flex.Item>
              {eventsForCell.map((ev, idx) => (
                <Flex.Item key={getEventKey(ev, idx)}>
                  {renderEventItem(ev, getEventKey(ev, idx))}
                </Flex.Item>
              ))}
            </Flex>
          </View>
        )
      }
    )

    const renderDayCell = useCallback(
      (cell, today, meta) => {
        // Use pre-computed events map for O(1) lookup instead of O(n) filter
        const eventsForCell = eventsByDate.get(cell.fullDate) || []

        return (
          <DayCell
            style={{ background: 'black' }}
            key={cell.fullDate}
            cell={cell}
            today={today}
            meta={meta}
            eventsForCell={eventsForCell}
            renderEventItem={renderEventItem}
            getEventKey={getEventKey}
          />
        )
      },
      [eventsByDate, renderEventItem, getEventKey]
    )

    // Memoize number of rows calculation
    const numRows = useMemo(() => Math.ceil(cells.length / 7), [cells.length])

    // Memoize calendar rows to prevent unnecessary re-renders
    const calendarRows = useMemo(() => {
      return (
        <table
          style={{
            borderCollapse: 'collapse',
            width: '100%',
            tableLayout: 'fixed',
            maxWidth: '100%'
          }}
        >
          <thead>
            <tr style={{ borderBottom: 'none' }}>
              {daysOfWeek.map((day) => (
                <th
                  key={day}
                  style={{
                    padding: '8px',
                    textAlign: 'center'
                  }}
                >
                  <Text>{day}</Text>
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {[...Array(numRows)].map((_, rowIdx) => (
              <tr
                key={rowIdx}
                style={{
                  height: '120px',
                  minHeight: '120px',
                  maxHeight: '200px'
                }}
              >
                {cells.slice(rowIdx * 7, (rowIdx + 1) * 7).map((cell) => (
                  <td
                    key={cell.fullDate}
                    style={{
                      border: '1px solid #ddd',
                      width: '14.28%', // Equal width for 7 columns
                      minHeight: '120px',
                      maxHeight: '200px',
                      overflow: 'hidden',
                      verticalAlign: 'top',
                      position: 'relative',
                      boxSizing: 'border-box',
                      wordWrap: 'break-word',
                      wordBreak: 'break-word'
                    }}
                  >
                    {renderDayCell(cell, today, meta)}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      )
    }, [numRows, cells, renderDayCell, today, meta, daysOfWeek])

    if (loading)
      return (
        <View as="div" textAlign="center" padding="small">
          <Text color="secondary">Loading events...</Text>
        </View>
      )

    return (
      <View
        as="div"
        style={{
          width: '100%',
          maxWidth: '100%',
          overflow: 'hidden',
          boxSizing: 'border-box'
        }}
      >
        <div
          style={{
            width: '100%',
            maxWidth: '100%',
            overflow: 'hidden',
            boxSizing: 'border-box'
          }}
        >
          {calendarRows}
        </div>
      </View>
    )
  }
)

export default MonthView
