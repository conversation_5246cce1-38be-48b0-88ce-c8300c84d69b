import React, { useState } from 'react'
import { View } from '@instructure/ui-view'
import { Text } from '@instructure/ui-text'
import { ToggleDetails } from '@instructure/ui-toggle-details'
import { Flex } from '@instructure/ui-flex'
import {
  IconAssignmentLine,
  IconQuizLine,
  IconDocumentLine,
  IconDiscussionLine,
  IconExternalLinkLine,
  IconPaperclipLine
} from '@instructure/ui-icons'
import { Popover } from '@instructure/ui-popover'
import { CloseButton } from '@instructure/ui-buttons'

const UndatedItems = ({ items, colorMap, loading = false }) => {
  const [isShowingContent, setIsShowingContent] = useState(null)

  const getItemIcon = (itemType) => {
    switch (itemType) {
      case 'assignment':
        return <IconAssignmentLine />
      case 'quizzes::quiz':
      case 'quiz':
        return <IconQuizLine />
      case 'wiki_page':
      case 'page':
        return <IconDocumentLine />
      case 'discussion_topic':
      case 'discussion':
        return <IconDiscussionLine />
      case 'external_tool':
        return <IconExternalLinkLine />
      case 'file':
        return <IconPaperclipLine />
      default:
        return <IconAssignmentLine />
    }
  }

  const toggleSummary = () => (
    <View padding="x-small none">
      UNDATED
      <hr
        style={{
          border: 'none',
          borderTop: '1px solid #e6e7e9',
          margin: '4px 0 0 0'
        }}
      />
    </View>
  )

  const getItemKey = (item, index) =>
    item.id ||
    item.html_url ||
    `${item.context_code || 'ctx'}:${item.type || 'item'}:${item.title || 'untitled'}:${index}`

  const renderedItems = (item, key) => {
    const icon = getItemIcon(item.type)
    const itemColor = colorMap[item.context_code] || '#000000'
    return (
      <Popover
        key={key}
        renderTrigger={
          <View
            as="div"
            key={key}
            padding="x-small none none none"
            cursor="pointer"
          >
            <Text
              as="div"
              size="contentSmall"
              color="brand"
              themeOverride={{ brandColor: itemColor }}
            >
              {icon} {item.title}
            </Text>
          </View>
        }
        isShowingContent={isShowingContent === key}
        onShowContent={() => setIsShowingContent(key)}
        onHideContent={() => setIsShowingContent(null)}
        on="click"
        screenReaderLabel="Popover Dialog Example"
        shouldContainFocus
        shouldReturnFocus
        shouldCloseOnDocumentClick
        offsetY="16px"
      >
        <View padding="small" display="block" as="div">
          <Flex direction="column" margin="none none small" gap="small">
            <Flex.Item key={key}>
              <CloseButton
                placement="end"
                onClick={() => setIsShowingContent(false)}
                screenReaderLabel="Close"
              />
            </Flex.Item>
            <Flex.Item>
              <Text size="descriptionPage">{item.title}</Text>
            </Flex.Item>
            <hr
              style={{
                border: 'none',
                borderTop: '1px solid #e6e7e9',
                margin: '8px 0 0 0'
              }}
            />
            <Flex.Item>
              Calendar: <Text>{item.context_name}</Text>
            </Flex.Item>
            {item.description && (
              <Flex.Item>
                Details: <Text>{item.description}</Text>
              </Flex.Item>
            )}
            {item.location_name && (
              <Flex.Item>
                Location: <Text>{item.location_name}</Text>
              </Flex.Item>
            )}
          </Flex>
        </View>
      </Popover>
    )
  }

  return (
    <View>
      <ToggleDetails
        summary={toggleSummary()}
        fluidWidth
        defaultExpanded={false}
      >
        <View>
          {loading ? (
            <Text color="secondary">Loading…</Text>
          ) : !items || items.length === 0 ? (
            <Text color="secondary" size="x-small">
              No undated Items
            </Text>
          ) : (
            items.map((item, index) =>
              renderedItems(item, getItemKey(item, index))
            )
          )}
        </View>
      </ToggleDetails>
    </View>
  )
}

export default UndatedItems
