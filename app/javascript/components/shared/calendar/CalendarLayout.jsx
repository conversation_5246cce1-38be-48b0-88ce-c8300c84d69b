import React, { useState, useEffect, useMemo } from 'react'
import { View } from '@instructure/ui-view'
import { Link } from '@instructure/ui-link'
import { Flex } from '@instructure/ui-flex'
import { Button } from '@instructure/ui-buttons'
import { IconArrowStartLine, IconArrowEndLine } from '@instructure/ui-icons'
import { Calendar } from '@instructure/ui-calendar'
import { DateInput } from '@instructure/ui-date-input'
import MonthView from './MonthView'
import WeekView from './WeekView'
import AgendaView from './AgendaView'
import UserCalendarsList from './UserCalendarsList'
import UndatedItems from './UndatedItems'
import * as API from './../../../utils/api'

// ============================================================================
// CACHE AND API UTILITIES
// ============================================================================

const eventCache = new Map()
const CACHE_DURATION = 5 * 60 * 1000

const getCacheKey = (startDate, endDate, context_codes, studentId) => {
  return `${studentId}-${startDate}-${endDate}-${Array.from(context_codes).sort().join(',')}`
}

const formatDateToISO = (date) => {
  return date ? new Date(date).toISOString() : null
}

const getMonthlyEventsAPI = async (
  startDate,
  endDate,
  context_codes,
  studentId
) => {
  const cacheKey = getCacheKey(startDate, endDate, context_codes, studentId)
  const now = Date.now()

  if (eventCache.has(cacheKey)) {
    const cached = eventCache.get(cacheKey)
    if (now - cached.timestamp < CACHE_DURATION) {
      console.log('Using cached events for', cacheKey)
      return cached.data
    } else {
      eventCache.delete(cacheKey)
    }
  }

  console.log(
    'Fetching events from',
    startDate,
    'to',
    endDate,
    'for context codes:',
    context_codes
  )

  try {
    const payload = {
      start_date: formatDateToISO(startDate),
      end_date: formatDateToISO(endDate),
      context_codes
    }

    const response = await API.fetchCalendarEvents(studentId, payload)

    // Handle the grouped events structure from the API
    const eventsData = response.data?.events || response.data || {}

    let flattenedEvents = []

    // If events is an object with date keys, flatten it into an array
    if (typeof eventsData === 'object' && !Array.isArray(eventsData)) {
      Object.keys(eventsData).forEach((dateKey) => {
        const eventsForDate = eventsData[dateKey]
        if (Array.isArray(eventsForDate)) {
          flattenedEvents.push(...eventsForDate)
        }
      })
    } else if (Array.isArray(eventsData)) {
      flattenedEvents = eventsData
    }

    // Cache the result
    eventCache.set(cacheKey, {
      data: flattenedEvents,
      timestamp: now
    })

    // Clean up old cache entries
    if (eventCache.size > 50) {
      const oldestKey = eventCache.keys().next().value
      eventCache.delete(oldestKey)
    }

    return flattenedEvents
  } catch (error) {
    console.error('Error fetching calendar events:', error)
    return []
  }
}

// ============================================================================
// MAIN CALENDAR COMPONENT
// ============================================================================

const CalendarLayout = ({ studentId }) => {
  // Constants
  const daysOfWeek = ['MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT', 'SUN']
  const today = new Date()

  // ============================================================================
  // UTILITY FUNCTIONS
  // ============================================================================

  const getCalendarMeta = (date) => {
    const year = date.getFullYear()
    const month = date.getMonth()
    const firstDay = new Date(year, month, 1)
    const lastDay = new Date(year, month + 1, 0)
    const daysInMonth = lastDay.getDate()
    const startDayIdx = (firstDay.getDay() + 6) % 7 // 0=Mon, 6=Sun
    const prevMonthLastDay = new Date(year, month, 0).getDate()
    return { year, month, daysInMonth, startDayIdx, prevMonthLastDay }
  }

  // ============================================================================
  // STATE MANAGEMENT
  // ============================================================================

  const [displayedDate, setDisplayedDate] = useState(
    new Date(today.getFullYear(), today.getMonth(), 1)
  )
  const [viewType, setViewType] = useState('month')
  const [weekIndex, setWeekIndex] = useState(0)
  const [userCalendars, setUserCalendars] = useState([])
  const [selectedCalendars, setSelectedCalendars] = useState(new Set())
  const [calendarSelectionOrder, setCalendarSelectionOrder] = useState([]) // Track order for 10-calendar limit
  const [userUndatedItems, setUserUndatedItems] = useState([])
  const [loadingUndated, setLoadingUndated] = useState(false)
  const [events, setEvents] = useState([])
  const [loadingEvents, setLoadingEvents] = useState(false)
  const [selectedWeekDate, setSelectedWeekDate] = useState(new Date())
  const [selectedAgendaDate, setSelectedAgendaDate] = useState(new Date())
  const [showDateSelector, setShowDateSelector] = useState(false)

  // ============================================================================
  // COMPUTED VALUES
  // ============================================================================

  const meta = getCalendarMeta(displayedDate)

  const cells = useMemo(() => {
    const cells = []
    const pad = (n) => String(n).padStart(2, '0')
    // Previous month days
    for (let i = 0; i < meta.startDayIdx; i++) {
      let year = meta.month === 0 ? meta.year - 1 : meta.year
      let month = meta.month === 0 ? 11 : meta.month - 1
      let day = meta.prevMonthLastDay - meta.startDayIdx + i + 1
      let fullDate = `${year}-${pad(month + 1)}-${pad(day)}`
      cells.push({ day, type: 'prev', fullDate })
    }
    // Current month days
    for (let d = 1; d <= meta.daysInMonth; d++) {
      let fullDate = `${meta.year}-${pad(meta.month + 1)}-${pad(d)}`
      cells.push({ day: d, type: 'current', fullDate })
    }
    // Next month days
    let nextDay = 1
    let nextMonth = meta.month === 11 ? 0 : meta.month + 1
    let nextYear = meta.month === 11 ? meta.year + 1 : meta.year
    // Fill up to next multiple of 7 (35 or 42)
    const totalCells =
      cells.length % 7 === 0
        ? cells.length
        : cells.length + (7 - (cells.length % 7))

    while (cells.length < totalCells) {
      let fullDate = `${nextYear}-${pad(nextMonth + 1)}-${pad(nextDay)}`
      cells.push({ day: nextDay++, type: 'next', fullDate })
    }
    return cells
  }, [
    meta.year,
    meta.month,
    meta.startDayIdx,
    meta.daysInMonth,
    meta.prevMonthLastDay
  ])

  // Keep weekIndex in sync with selectedWeekDate so the week view shows the selected week
  useEffect(() => {
    const pad = (n) => String(n).padStart(2, '0')
    const day = selectedWeekDate.getDay()
    const diff = selectedWeekDate.getDate() - day + (day === 0 ? -6 : 1) // Monday
    const monday = new Date(selectedWeekDate)
    monday.setDate(diff)
    const mondayStr = `${monday.getFullYear()}-${pad(monday.getMonth() + 1)}-${pad(monday.getDate())}`
    const idx = cells.findIndex((c) => c.fullDate === mondayStr)
    if (idx !== -1) {
      const newWeekIndex = Math.floor(idx / 7)
      if (newWeekIndex !== weekIndex) setWeekIndex(newWeekIndex)
    }
  }, [selectedWeekDate, cells, weekIndex])

  // Sync displayedDate with selectedWeekDate for week view
  useEffect(() => {
    if (viewType === 'week') {
      const weekYear = selectedWeekDate.getFullYear()
      const weekMonth = selectedWeekDate.getMonth()
      const currentDisplayedYear = displayedDate.getFullYear()
      const currentDisplayedMonth = displayedDate.getMonth()

      if (
        weekYear !== currentDisplayedYear ||
        weekMonth !== currentDisplayedMonth
      ) {
        setDisplayedDate(new Date(weekYear, weekMonth, 1))
      }
    }
  }, [selectedWeekDate, viewType, displayedDate])

  // Sync displayedDate with selectedAgendaDate for agenda view
  useEffect(() => {
    if (viewType === 'agenda') {
      const agendaYear = selectedAgendaDate.getFullYear()
      const agendaMonth = selectedAgendaDate.getMonth()
      const currentDisplayedYear = displayedDate.getFullYear()
      const currentDisplayedMonth = displayedDate.getMonth()

      if (
        agendaYear !== currentDisplayedYear ||
        agendaMonth !== currentDisplayedMonth
      ) {
        setDisplayedDate(new Date(agendaYear, agendaMonth, 1))
      }
    }
  }, [selectedAgendaDate, viewType, displayedDate])

  const { startDate, endDate } = useMemo(() => {
    const pad = (n) => String(n).padStart(2, '0')
    let startYear, startMonth
    if (cells[0].type === 'prev') {
      startYear = meta.month === 0 ? meta.year - 1 : meta.year
      startMonth = meta.month === 0 ? 11 : meta.month - 1
    } else {
      startYear = meta.year
      startMonth = meta.month
    }
    let endYear, endMonth
    if (cells[cells.length - 1].type === 'next') {
      endYear = meta.month === 11 ? meta.year + 1 : meta.year
      endMonth = meta.month === 11 ? 0 : meta.month + 1
    } else {
      endYear = meta.year
      endMonth = meta.month
    }
    const startDate = `${startYear}-${pad(startMonth + 1)}-${pad(cells[0].day)}`
    const endDate = `${endYear}-${pad(endMonth + 1)}-${pad(cells[cells.length - 1].day)}`
    return { startDate, endDate }
  }, [cells, meta.year, meta.month])

  const { weekStart, weekEnd } = useMemo(() => {
    const date = selectedWeekDate
    const day = date.getDay()
    const diff = date.getDate() - day + (day === 0 ? -6 : 1)
    const monday = new Date(date)
    monday.setDate(diff)
    const sunday = new Date(monday)
    sunday.setDate(monday.getDate() + 6)
    const pad = (n) => String(n).padStart(2, '0')
    const weekStart = `${monday.getFullYear()}-${pad(monday.getMonth() + 1)}-${pad(monday.getDate())}`
    const weekEnd = `${sunday.getFullYear()}-${pad(sunday.getMonth() + 1)}-${pad(sunday.getDate())}`
    return { weekStart, weekEnd }
  }, [selectedWeekDate])

  // ============================================================================
  // EVENT HANDLERS
  // ============================================================================

  const handlePrevMonth = () => {
    switch (viewType) {
      case 'month':
        setDisplayedDate((prev) => {
          const year = prev.getFullYear()
          const month = prev.getMonth()
          return new Date(year, month - 1, 1)
        })
        break
      case 'week':
        setSelectedWeekDate((prev) => {
          const newDate = new Date(prev)
          newDate.setDate(newDate.getDate() - 7) // Go back one week
          return newDate
        })
        break
      case 'agenda':
        setSelectedAgendaDate((prev) => {
          const newDate = new Date(prev)
          newDate.setMonth(newDate.getMonth() - 1) // Go back one month
          return newDate
        })
        break
    }
  }

  const handleNextMonth = () => {
    switch (viewType) {
      case 'month':
        setDisplayedDate((prev) => {
          const year = prev.getFullYear()
          const month = prev.getMonth()
          return new Date(year, month + 1, 1)
        })
        break
      case 'week':
        setSelectedWeekDate((prev) => {
          const newDate = new Date(prev)
          newDate.setDate(newDate.getDate() + 7) // Go forward one week
          return newDate
        })
        break
      case 'agenda':
        setSelectedAgendaDate((prev) => {
          const newDate = new Date(prev)
          newDate.setMonth(newDate.getMonth() + 1) // Go forward one month
          return newDate
        })
        break
    }
  }

  const handleToday = () => {
    const todayDate = new Date()
    setDisplayedDate(new Date(todayDate.getFullYear(), todayDate.getMonth(), 1))
    setSelectedWeekDate(new Date(todayDate))
    setSelectedAgendaDate(new Date(todayDate))
  }

  // ============================================================================
  // DATE RANGE FORMATTING
  // ============================================================================

  const formatMonthRange = useMemo(() => {
    const formatDate = (dateStr) => {
      const date = new Date(dateStr)
      return date.toLocaleDateString('en-GB', {
        day: 'numeric',
        month: 'short',
        year: 'numeric'
      })
    }
    const monthStart = `${meta.year}-${String(meta.month + 1).padStart(2, '0')}-01`
    const monthEnd = `${meta.year}-${String(meta.month + 1).padStart(2, '0')}-${new Date(meta.year, meta.month + 1, 0).getDate()}`
    return `${formatDate(monthStart)} – ${formatDate(monthEnd)}`
  }, [meta])

  const formatAgendaDate = useMemo(() => {
    return selectedAgendaDate.toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    })
  }, [selectedAgendaDate])

  // Handle calendar selection with 10-calendar limit (FIFO: removes oldest when selecting 11th)
  const handleCalendarToggle = (contextCode) => {
    setSelectedCalendars((prev) => {
      const newSet = new Set(prev)
      if (newSet.has(contextCode)) {
        // Deselecting a calendar
        newSet.delete(contextCode)
        return newSet
      } else {
        // Selecting a new calendar
        if (newSet.size >= 10) {
          // Remove the first selected calendar if we're at the limit
          setCalendarSelectionOrder((prevOrder) => {
            const newOrder = [...prevOrder]
            if (newOrder.length > 0) {
              const firstSelected = newOrder.shift() // Remove first item
              newSet.delete(firstSelected) // Remove from selected set
            }
            newOrder.push(contextCode) // Add new selection to end
            return newOrder
          })
        } else {
          // Add to selection order if under limit
          setCalendarSelectionOrder((prevOrder) => [...prevOrder, contextCode])
        }
        newSet.add(contextCode)
        return newSet
      }
    })

    // Also update the selection order when deselecting
    setCalendarSelectionOrder((prevOrder) => {
      if (!prevOrder.includes(contextCode)) {
        return prevOrder // No change if not in order
      }
      return prevOrder.filter((code) => code !== contextCode)
    })
  }

  // ============================================================================
  // RENDER FUNCTIONS
  // ============================================================================

  const renderCalendarHeader = () => {
    const btnOverrides = {
      primaryBackground: '#696969',
      primaryBorderColor: '#696969',
      primaryActiveBackground: '#696969',
      primaryHoverBackground: '#696969'
    }
    return (
      <Flex>
        <Flex.Item shouldGrow shouldShrink>
          <Button padding="small" onClick={handleToday}>
            Today
          </Button>
          <View as="span" padding="small" className="nav-button-group">
            <Button
              renderIcon={IconArrowStartLine}
              id="prev-month"
              onClick={handlePrevMonth}
            />
            <Button
              renderIcon={IconArrowEndLine}
              id="next-month"
              onClick={handleNextMonth}
            />
          </View>
          {viewType === 'month' && (
            <View as="span" padding="small">
              <Link
                onClick={() => setShowDateSelector(!showDateSelector)}
                isWithinText={false}
              >
                {formatMonthRange}
              </Link>
              {showDateSelector && (
                <DateInput
                  label="Select Month"
                  value={displayedDate.toISOString().slice(0, 10)}
                  placement="bottom"
                  onChange={(event, data) => {
                    const inputValue = data?.value || event?.target?.value
                    const parsedDate = new Date(inputValue)
                    if (!isNaN(parsedDate.getTime())) {
                      setDisplayedDate(
                        new Date(
                          parsedDate.getFullYear(),
                          parsedDate.getMonth(),
                          1
                        )
                      )
                      setShowDateSelector(false)
                    }
                  }}
                />
              )}
            </View>
          )}
          {viewType === 'week' && (
            <View as="span" padding="small">
              <Link
                onClick={() => setShowDateSelector(!showDateSelector)}
                isWithinText={false}
              >
                {formatWeekRange}
              </Link>
              {showDateSelector && (
                <DateInput
                  label="Select Week"
                  value={selectedWeekDate.toISOString().slice(0, 10)}
                  placement="bottom"
                  onChange={(event, data) => {
                    const inputValue = data?.value || event?.target?.value
                    const parsedDate = new Date(inputValue)
                    if (!isNaN(parsedDate.getTime())) {
                      const day = parsedDate.getDay()
                      const diff =
                        parsedDate.getDate() - day + (day === 0 ? -6 : 1) // Monday as start
                      const monday = new Date(parsedDate)
                      monday.setDate(diff)
                      setSelectedWeekDate(monday)
                      setShowDateSelector(false)
                    }
                  }}
                />
              )}
            </View>
          )}
          {viewType === 'agenda' && (
            <View as="span" padding="small">
              <Link
                onClick={() => setShowDateSelector(!showDateSelector)}
                isWithinText={false}
              >
                {formatAgendaDate}
              </Link>
              {showDateSelector && (
                <DateInput
                  label="Select Start Date"
                  value={selectedAgendaDate.toISOString().slice(0, 10)}
                  placement="bottom"
                  onChange={(event, data) => {
                    const inputValue = data?.value || event?.target?.value
                    const parsedDate = new Date(inputValue)
                    if (!isNaN(parsedDate.getTime())) {
                      setSelectedAgendaDate(parsedDate)
                      setShowDateSelector(false)
                    }
                  }}
                />
              )}
            </View>
          )}
        </Flex.Item>
        <Flex.Item>
          <View as="span" padding="small" className="view-type-btn-group">
            <Button
              color={viewType === 'week' ? 'primary' : 'secondary'}
              onClick={() => setViewType('week')}
              themeOverride={btnOverrides}
            >
              Week
            </Button>
            <Button
              color={viewType === 'month' ? 'primary' : 'secondary'}
              onClick={() => setViewType('month')}
              themeOverride={btnOverrides}
            >
              Month
            </Button>
            <Button
              color={viewType === 'agenda' ? 'primary' : 'secondary'}
              onClick={() => setViewType('agenda')}
              themeOverride={btnOverrides}
            >
              Agenda
            </Button>
          </View>
        </Flex.Item>
      </Flex>
    )
  }

  // ============================================================================
  // EFFECTS
  // ============================================================================

  useEffect(() => {
    if (!studentId) {
      setUserCalendars([])
      setSelectedCalendars(new Set())
      setCalendarSelectionOrder([])
      return
    }
    const fetchCalendars = async () => {
      try {
        const response = await API.fetchUserCalendars(studentId)
        const calendars = response.data || []
        setUserCalendars(calendars)
        // Initialize calendars as selected by default (max 10)
        const allContextCodes = calendars
          .map((cal) => cal.context_code)
          .filter(Boolean)
        const selectedCodes = allContextCodes.slice(0, 10) // Limit to first 10
        setSelectedCalendars(new Set(selectedCodes))
        setCalendarSelectionOrder(selectedCodes)
      } catch (error) {
        if (!isCancelled) {
          console.error(error)
          setUserCalendars([])
          setSelectedCalendars(new Set())
          setCalendarSelectionOrder([])
        }
      }
    }

    fetchCalendars()
  }, [studentId])

  useEffect(() => {
    const fetchEvents = async () => {
      if (!studentId || selectedCalendars.size === 0) {
        setEvents([])
        return
      }

      setLoadingEvents(true)
      try {
        const contextCodes = Array.from(selectedCalendars)
        const currentStart =
          viewType === 'week'
            ? weekStart
            : viewType == 'agenda'
              ? selectedAgendaDate
              : startDate
        const currentEnd =
          viewType === 'week' ? weekEnd : viewType == 'agenda' ? null : endDate
        const fetchedEvents = await getMonthlyEventsAPI(
          currentStart,
          currentEnd,
          contextCodes,
          studentId
        )
        setEvents(Array.isArray(fetchedEvents) ? fetchedEvents : [])
      } catch (error) {
        console.error('Error fetching events:', error)
        setEvents([])
      } finally {
        setLoadingEvents(false)
      }
    }

    fetchEvents()
  }, [
    viewType,
    startDate,
    endDate,
    weekStart,
    weekEnd,
    selectedCalendars,
    selectedAgendaDate,
    studentId
  ])

  useEffect(() => {
    if (!studentId || selectedCalendars.size === 0) {
      setUserUndatedItems([])
      return
    }
    setLoadingUndated(true)
    const contextCodes = Array.from(selectedCalendars)
    API.fetchCalendarEvents(studentId, {
      undated: true,
      type: 'assignment',
      context_codes: contextCodes
    })
      .then((response) => {
        const data = response?.data?.events
        setUserUndatedItems(data?.undated || [])
      })
      .catch(() => {
        setUserUndatedItems([])
      })
      .finally(() => setLoadingUndated(false))
  }, [studentId, selectedCalendars])

  const formatWeekRange = useMemo(() => {
    const formatDate = (dateStr) => {
      const date = new Date(dateStr)
      return date.toLocaleDateString('en-GB', {
        day: 'numeric',
        month: 'short',
        year: 'numeric'
      })
    }
    return `${formatDate(weekStart)} – ${formatDate(weekEnd)}`
  }, [weekStart, weekEnd])

  const colorMap = useMemo(() => {
    const map = {}
    userCalendars.forEach((cal) => {
      map[cal.context_code] = cal.color
    })
    return map
  }, [userCalendars])

  const eventDates = useMemo(() => {
    const dates = new Set()
    events.forEach((event) => {
      const date = event.date
        ? event.date.slice(0, 10)
        : event.start_at
          ? event.start_at.slice(0, 10)
          : null
      if (date) dates.add(date)
    })
    return dates
  }, [events])

  // ============================================================================
  // MAIN RENDER
  // ============================================================================

  return (
    <Flex gap="medium">
      <Flex.Item size="75%" align="start">
        <View as="div" padding="small" id="cal-content-wrapper">
          <View as="div" id="cal-header-bar">
            {renderCalendarHeader()}
          </View>
          <hr
            style={{
              border: 'none',
              borderTop: '1px solid #e6e7e9',
              margin: '8px 0 0 0'
            }}
          />
          <View
            as="div"
            id="cal-left-side-content"
            padding="none none none none"
          >
            {(() => {
              switch (viewType) {
                case 'month':
                  return (
                    <MonthView
                      daysOfWeek={daysOfWeek}
                      meta={meta}
                      today={today}
                      colorMap={colorMap}
                      events={events}
                      loading={loadingEvents}
                    />
                  )
                case 'week':
                  return (
                    <WeekView
                      daysOfWeek={daysOfWeek}
                      cells={cells}
                      weekIndex={weekIndex}
                      events={events}
                      colorMap={colorMap}
                      loading={loadingEvents}
                      onDayClick={(fullDate) => {
                        // fullDate is 'YYYY-MM-DD'
                        const [y, m, d] = fullDate.split('-').map(Number)
                        const clicked = new Date(y, m - 1, d)
                        if (!isNaN(clicked.getTime())) {
                          const day = clicked.getDay()
                          const diff =
                            clicked.getDate() - day + (day === 0 ? -6 : 1)
                          const monday = new Date(clicked)
                          monday.setDate(diff)
                          setSelectedWeekDate(monday)
                          setShowDateSelector(true)
                        }
                      }}
                    />
                  )
                case 'agenda':
                  return (
                    <AgendaView
                      events={events}
                      colorMap={colorMap}
                      loading={loadingEvents}
                      selectedDate={selectedAgendaDate}
                    />
                  )
                default:
                  return null
              }
            })()}
          </View>
        </View>
      </Flex.Item>
      <Flex.Item size="25%" align="start">
        <View as="div" padding="small" id="cal-right-side-wrapper">
          <Calendar
            visibleMonth={
              displayedDate.getFullYear() +
              '-' +
              String(displayedDate.getMonth() + 1).padStart(2, '0')
            }
            onDateSelected={(date) => {
              // date is a string in 'YYYY-MM-DD' format
              const [year, month] = date.split('-').map(Number)
              setDisplayedDate(new Date(year, month - 1, 1))
            }}
            onRequestRenderNextMonth={handleNextMonth}
            onRequestRenderPrevMonth={handlePrevMonth}
            renderDay={(date) => {
              const dateStr = date.toISOString().slice(0, 10)
              const hasEvent = eventDates.has(dateStr)
              const style = hasEvent
                ? { backgroundColor: '#e6f7ff', border: '1px solid #1890ff' }
                : {}
              return <div style={style}>{date.getDate()}</div>
            }}
          />
          <View as="div" padding="small none">
            <UserCalendarsList
              calendars={userCalendars}
              selectedCalendars={selectedCalendars}
              onCalendarToggle={handleCalendarToggle}
            />
          </View>
          <View as="div" padding="small none">
            <UndatedItems
              items={userUndatedItems}
              colorMap={colorMap}
              loading={loadingUndated}
            />
          </View>
        </View>
      </Flex.Item>
    </Flex>
  )
}

export default CalendarLayout
