import React, { useMemo, useState } from 'react'
import { View } from '@instructure/ui-view'
import { Text } from '@instructure/ui-text'
import { TruncateText } from '@instructure/ui-truncate-text'
import { Popover } from '@instructure/ui-popover'
import { CloseButton } from '@instructure/ui-buttons'
import { Flex } from '@instructure/ui-flex'
import {
  IconAssignmentLine,
  IconQuizLine,
  IconCalendarMonthLine,
  IconDiscussionLine,
  IconNoteLine,
  IconAnnouncementLine
} from '@instructure/ui-icons'

const hours = [
  '00',
  '01',
  '02',
  '03',
  '04',
  '05',
  '06',
  '07',
  '08',
  '09',
  '10',
  '11',
  '12',
  '13',
  '14',
  '15',
  '16',
  '17',
  '18',
  '19',
  '20',
  '21',
  '22',
  '23'
]

const ICONS = {
  assignment: IconAssignmentLine,
  discussion_topic: IconDiscussionLine,
  quiz: IconQuizLine,
  announcement: IconAnnouncementLine,
  event: IconCalendarMonthLine,
  planner_note: IconNoteLine
}

const WeekView = ({
  daysOfWeek,
  cells,
  renderDayCell,
  weekIndex,
  events = [],
  colorMap = {},
  loading = false
}) => {
  // weekIndex: which week to show (0-based)
  const startIdx = weekIndex * 7
  const weekCells = cells.slice(startIdx, startIdx + 7)

  // Track which event popover is open in week view
  const [openPopoverKey, setOpenPopoverKey] = useState(null)

  // Process events by date and separate all-day from timed events
  const { allDayEventsByDate, timedEventsByDate } = useMemo(() => {
    const allDayEvents = new Map()
    const timedEvents = new Map()

    events.forEach((event) => {
      let eventDate = null

      if (event.date) {
        // Convert UTC date to local date
        const utcDate = new Date(event.date)
        eventDate = utcDate.toLocaleDateString('en-CA') // YYYY-MM-DD format in local timezone
      } else if (event.start_at) {
        // Convert UTC timestamp to local date
        const utcDate = new Date(event.start_at)
        eventDate = utcDate.toLocaleDateString('en-CA') // YYYY-MM-DD format in local timezone
      }

      if (!eventDate) return

      if (event.all_day) {
        if (!allDayEvents.has(eventDate)) {
          allDayEvents.set(eventDate, [])
        }
        allDayEvents.get(eventDate).push(event)
      } else {
        if (!timedEvents.has(eventDate)) {
          timedEvents.set(eventDate, new Map())
        }

        // Group timed events by hour
        const startTime = new Date(event.start_at)
        const hour = startTime.getHours().toString().padStart(2, '0')

        if (!timedEvents.get(eventDate).has(hour)) {
          timedEvents.get(eventDate).set(hour, [])
        }
        timedEvents.get(eventDate).get(hour).push(event)
      }
    })

    return { allDayEventsByDate: allDayEvents, timedEventsByDate: timedEvents }
  }, [events])

  // Render events for specific hour
  const renderTimedEvents = (date, hour = null) => {
    const dateEvents = timedEventsByDate.get(date)
    if (!dateEvents) return null

    const eventDate = new Date(date).getDate()
    const eventMonth = new Date(date).toLocaleString('en-US', {
      month: 'short'
    })

    let hourEvents = []

    if (!hour) {
      hourEvents = allDayEventsByDate.get(date) || []
    } else {
      hourEvents = dateEvents.get(hour) || []
    }

    return (
      <Flex direction={hour == null ? 'column' : 'row'}>
        {hourEvents.map((event, idx) => {
          const eventColor = colorMap[event.context_code] || '#2b7abc'
          const startTime = event.start_at ? new Date(event.start_at) : null
          const endTime = event.end_at ? new Date(event.end_at) : null
          const due_at = event.due_at ? new Date(event.due_at) : null
          const day = due_at ? due_at.getDate() : null
          const month = due_at
            ? due_at.toLocaleString('en-US', { month: 'short' })
            : null

          const EventIcon = ICONS[event.type] || IconCalendarMonthLine

          const timeDisplay = due_at
            ? due_at.toLocaleTimeString('en-US', {
                hour: 'numeric',
                minute: '2-digit',
                hour12: false
              })
            : `${startTime.toLocaleTimeString('en-US', {
                hour: 'numeric',
                minute: '2-digit',
                hour12: false
              })}${
                endTime
                  ? ` - ${endTime.toLocaleTimeString('en-US', {
                      hour: 'numeric',
                      minute: '2-digit',
                      hour12: false
                    })}`
                  : ''
              }`

          const due_time = due_at
            ? due_at.toLocaleTimeString('en-US', {
                hour: 'numeric',
                minute: '2-digit',
                hour12: false
              })
            : null
          const formatted_due_date = due_time
            ? `${day} ${month} at ${due_time}`
            : null

          const key = `timed-${date}-${hour}-${event.id || idx}`

          return (
            <Flex.Item key={key} size={`calc(100%/${hourEvents.length})`}>
              <Popover
                key={key}
                renderTrigger={(triggerProps) => (
                  <View
                    {...triggerProps}
                    as="div"
                    cursor="pointer"
                    borderRadius="small"
                    padding="xxx-small"
                    margin="xxx-small none"
                    style={{
                      backgroundColor: eventColor,
                      color: 'white',
                      fontSize: '10px',
                      lineHeight: '1.2'
                    }}
                  >
                    <Flex direction={hour == null ? 'row' : 'column'}>
                      <Flex.Item>
                        <EventIcon
                          style={{
                            color: eventColor
                          }}
                        />
                        &nbsp;
                      </Flex.Item>
                      <Flex.Item>
                        {hour != null && (
                          <div
                            style={{ fontWeight: 'light', fontSize: '0.8rem' }}
                          >
                            <TruncateText>{timeDisplay}</TruncateText>
                          </div>
                        )}
                      </Flex.Item>
                      <Flex.Item>
                        <span
                          style={{
                            color: eventColor,
                            fontWeight: 'light',
                            fontSize: '0.9rem'
                          }}
                        >
                          <TruncateText>{event.title}</TruncateText>
                        </span>
                      </Flex.Item>
                    </Flex>
                  </View>
                )}
                on="click"
                screenReaderLabel="Event details"
                shouldContainFocus
                shouldReturnFocus
                shouldCloseOnDocumentClick
                isShowingContent={openPopoverKey === key}
                onShowContent={() => setOpenPopoverKey(key)}
                onHideContent={() => setOpenPopoverKey(null)}
              >
                <View padding="small" display="block" as="div">
                  <Flex direction="column" margin="none none small" gap="small">
                    <Flex.Item>
                      <CloseButton
                        placement="end"
                        onClick={() => setOpenPopoverKey(null)}
                        screenReaderLabel="Close"
                      />
                    </Flex.Item>
                    <Flex.Item>
                      {' '}
                      <Text size="descriptionPage">{event.title}</Text>
                    </Flex.Item>
                    <hr
                      style={{
                        border: 'none',
                        borderTop: '1px solid #e6e7e9',
                        margin: '8px 0 0 0'
                      }}
                    />
                    <Flex.Item>
                      {`${eventDate} ${eventMonth}, ${timeDisplay}`}
                    </Flex.Item>
                    {formatted_due_date && (
                      <Flex.Item>
                        {' '}
                        Due: <Text>{formatted_due_date}</Text>
                      </Flex.Item>
                    )}
                    <Flex.Item>
                      Calendar: <Text>{event.context_name}</Text>{' '}
                    </Flex.Item>
                    {event.description && (
                      <Flex.Item>
                        Details: <Text>{event.description}</Text>
                      </Flex.Item>
                    )}
                    {event.location_name && (
                      <Flex.Item>
                        Location: <Text>{event.location_name}</Text>
                      </Flex.Item>
                    )}
                  </Flex>
                </View>
              </Popover>
            </Flex.Item>
          )
        })}
      </Flex>
    )
  }

  if (loading)
    return (
      <View as="div" textAlign="center" padding="small">
        <Text color="secondary">Loading events...</Text>
      </View>
    )

  // Render as a semantic table to get a full surrounding border and clear cell borders
  return (
    <View as="div">
      <table
        style={{
          width: '100%',
          borderCollapse: 'collapse',
          tableLayout: 'fixed'
        }}
      >
        <thead>
          {/* Day headers row */}
          <tr>
            <th
              style={{
                padding: '8px',
                textAlign: 'center',
                fontWeight: 'bold',
                backgroundColor: '#ffffffff',
                width: '3.1rem'
              }}
            >
              {/* Empty corner for time labels */}
            </th>
            {weekCells.map((cell, idx) => {
              const date = new Date(cell.fullDate)
              const today = new Date()
              const isToday =
                cell.type === 'current' &&
                date.getDate() === today.getDate() &&
                date.getMonth() === today.getMonth() &&
                date.getFullYear() === today.getFullYear()
              const backgroundColor =
                isToday && cell.type === 'current' ? '#f5f5f5ff' : '#ffffffff'
              const label = `${daysOfWeek[idx]} ${date.getMonth() + 1}/${date.getDate()}`
              return (
                <th
                  key={cell.fullDate}
                  style={{
                    padding: '8px',
                    textAlign: 'center',
                    fontWeight: 'bold',
                    backgroundColor: backgroundColor
                  }}
                >
                  {label}
                </th>
              )
            })}
          </tr>
          <tr>
            <th
              style={{
                border: '1px solid #e6e7e9',
                padding: '4px 8px',
                color: '#5d6b76',
                textAlign: 'left',
                fontWeight: 'normal',
                width: '3rem'
              }}
            >
              all-day
            </th>
            {weekCells.map((cell) => {
              return (
                <th
                  key={`allday-${cell.fullDate}`}
                  style={{
                    border: '1px solid #e6e7e9',
                    minHeight: '60px',
                    verticalAlign: 'top',
                    fontWeight: 'normal',
                    padding: '4px'
                  }}
                >
                  <div
                    style={{
                      display: 'flex',
                      flexDirection: 'column',
                      gap: '2px'
                    }}
                  >
                    {renderTimedEvents(cell.fullDate)}
                  </div>
                </th>
              )
            })}
          </tr>
        </thead>
        <tbody>
          {/* Hourly rows */}
          {hours.map((hour, hourIdx) => (
            <tr
              key={hourIdx}
              style={{
                height: '3.5rem',
                minHeight: '3.5rem'
              }}
            >
              <td
                style={{
                  border: '1px solid #e6e7e9',
                  borderTop: 'none',
                  borderBottom: 'none',
                  padding: '4px 8px',
                  color: '#5d6b76',
                  whiteSpace: 'nowrap'
                }}
              >
                {hour}
              </td>
              {weekCells.map((cell) => {
                return (
                  <td
                    key={`${cell.fullDate}-${hourIdx}`}
                    style={{
                      border: '1px solid #e6e7e9',
                      borderTop: 'none',
                      borderBottom: 'none',
                      minHeight: '50px',
                      verticalAlign: 'top',
                      padding: '4px'
                    }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        flexDirection: 'column',
                        gap: '2px'
                      }}
                    >
                      {renderTimedEvents(cell.fullDate, hour)}
                    </div>
                  </td>
                )
              })}
            </tr>
          ))}
        </tbody>
      </table>
    </View>
  )
}

export default WeekView
