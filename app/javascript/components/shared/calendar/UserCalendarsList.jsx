import React, { useEffect, useState } from 'react'
import { View } from '@instructure/ui-view'
import { Text } from '@instructure/ui-text'
import { ToggleDetails } from '@instructure/ui-toggle-details'
import { ColorIndicator } from '@instructure/ui-color-picker'
import { Flex } from '@instructure/ui-flex'
import { IconMoreSolid } from '@instructure/ui-icons'

const ColoredCheckBox = ({
  color,
  label,
  contextCode,
  isSelected,
  onToggle
}) => {
  const handleClicked = () => {
    onToggle(contextCode)
  }

  const defaultColor = '#e6e7e9'

  return (
    <Flex width="95%">
      <Flex.Item size="20px">
        <span style={{ cursor: 'pointer' }}>
          <ColorIndicator
            color={isSelected ? color : defaultColor}
            shape="rectangle"
            onClick={handleClicked}
            themeOverride={{
              rectangleIndicatorSize: '0.85rem',
              rectangularIndicatorBorderRadius: '0.175rem'
            }}
            cursor="pointer"
          />
        </span>
      </Flex.Item>
      <Flex.Item shouldShrink shouldGrow>
        <Text size="contentSmall">{label}</Text>
      </Flex.Item>
    </Flex>
  )
}

const UserCalendarsList = ({
  calendars,
  selectedCalendars,
  onCalendarToggle
}) => {
  const toggleSummary = () => {
    return (
      <View padding="x-small none">
        Calendars
        <hr
          style={{
            border: 'none',
            borderTop: '1px solid #e6e7e9',
            margin: '4px 0 0 0'
          }}
        />
      </View>
    )
  }

  return (
    <View>
      <ToggleDetails
        summary={toggleSummary()}
        fluidWidth={true}
        defaultExpanded
      >
        <View>
          {calendars.map((calendar, index) => (
            <ColoredCheckBox
              key={index}
              color={calendar.color}
              label={calendar.name}
              contextCode={calendar.context_code}
              isSelected={selectedCalendars.has(calendar.context_code)}
              onToggle={onCalendarToggle}
            />
          ))}
        </View>
      </ToggleDetails>
    </View>
  )
}
export default UserCalendarsList
