.nav-button-group button {
  border-radius: 0 !important;
}

.nav-button-group button span {
  border-radius: 0 !important;
}

.nav-button-group button:first-child span {
  border-right: none !important;
}

.view-type-btn-group {
  display: flex;
  align-items: center;
}
.view-type-btn-group button {
  border-radius: 0 !important;
}
.view-type-btn-group button span {
  border-radius: 0 !important;
}
.view-type-btn-group button:first-child span {
  border-right: none !important;
}
.view-type-btn-group button:last-child span {
  border-left: none !important;
}

[data-cid="BaseButton Button"]:focus {
  /* outline-offset: 0 !important; */
  /* outline-color: #D7DADE !important; */
  outline: none !important;
}


#event-view{
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}