# frozen_string_literal: true

class Api::V1::StudentsController < ApplicationController
  include Pagination

  def index
    authorize! :read, User

    students = fetch_students

    students = students.where(search_query, search: "%#{params[:search]}%") if params[:search].present? && params[:search].length >= 3

    students = students.order("#{sort_key} #{sort_order}")
    @paginated_data = paginated(students, pagination_params)
  end

  def calendars
    authorize! :read, User

    student = fetch_students.find_by(canvas_id: params[:id])
    return render json: { error: 'Student not found' }, status: :not_found unless student

    user_colors = canvas_sync_client.get("api/v1/users/#{student.canvas_id}/colors?as_user_id=#{student.canvas_id}")
    context_codes = user_colors[:custom_colors]

    @calendar_data = []

    context_codes.each do |code, color|
      # Skip if not a user or course context code
      next unless (match_data = code.match(/\A(?<type>user|course)_(?<id>\d+)\z/))

      type = match_data[:type]
      id   = match_data[:id]

      name =
        case type
        when 'user'
          student.name
        when 'course'
          find_course_name_across_shards(id)
        end

      @calendar_data << { type: type, id: id, name: name, color: color, context_code: code }
    end

    render json: @calendar_data
  end

  def create_observer_link
    authorize! :create, StudentObserverLink

    student = fetch_students.find_by(canvas_id: params[:id])
    return render json: { error: 'Student not found' }, status: :not_found unless student

    current_user.against_shards do |shard_user|
      # Check if observer already has an active link
      existing_link = StudentObserverLink.active.for_observer(shard_user.canvas_id).first
      if existing_link
        return render json: {
          error: 'You already have an active observer link. Please end the current link before creating a new one.'
        }, status: :unprocessable_entity
      end
    end

    # Create the observer link
    observer_link = StudentObserverLink.new(
      observer_user_id: current_canvas_user_id,
      observed_student_id: student.canvas_id,
      expires_at: Time.now + 1.hours
    )

    if observer_link.save
      render json: {
        data: format_observer_link(observer_link),
        message: 'Observer link created successfully'
      }, status: :created
    else
      render json: {
        error: observer_link.errors.full_messages.join(', ')
      }, status: :unprocessable_entity
    end
  end

  private

  def fetch_students
    launch_context_id = current_ability.launch_context.canvas_id
    base_query = if current_ability.launch_context.is_a?(Account)
                   User.active_students_for_account(launch_context_id)
                 elsif current_ability.launch_context.is_a?(Course)
                   User.active_students_in_course_ids([launch_context_id])
                 else
                   User.none
                 end

    # Use includes to avoid N+1 queries
    base_query.includes(:pseudonyms)
  end

  def format_observer_link(observer_link)
    {
      organization_id: current_organization.id,
      id: observer_link.id,
      observer_user_id: observer_link.observer_user_id,
      observed_student: {
        canvas_id: observer_link.observed_student.canvas_id,
        sortable_name: observer_link.observed_student.sortable_name,
        name: observer_link.observed_student.name,
        sis_id: observer_link.observed_student.pseudonyms.first&.sis_id
      },
      created_at: observer_link.created_at,
      expires_at: observer_link.expires_at,
      renewed_at: observer_link.renewed_at,
      time_remaining_minutes: observer_link.time_remaining,
      can_be_renewed: observer_link.can_be_renewed?,
      status: observer_link.status
    }
  end

  def search_query
    <<~SQL
      users.first_name ILIKE :search OR
      users.last_name ILIKE :search OR
      users.sortable_name ILIKE :search OR
      users.sis_id ILIKE :search
    SQL
  end

  def sort_key
    return params[:sort_key] if %w[users.sortable_name].include?(params[:sort_key])

    'users.sortable_name'
  end

  def sort_order
    params[:sort_order].to_s.upcase.in?(%w[ASC DESC]) ? params[:sort_order] : 'ASC'
  end

  def find_course_name_across_shards(canvas_id)
    if canvas_id.to_i >= PandaPal::Organization::SHARD_OFFSET
      # Determine which shard this course belongs to
      target_org = PandaPal::Organization.for_canvas_shard(canvas_id.to_i)

      # Switch to the target shard and look for the course
      target_org&.switch_tenant do
        # Convert the sharded canvas_id back to the local canvas_id for that shard
        local_canvas_id = canvas_id.to_i % PandaPal::Organization::SHARD_OFFSET
        course = Course.find_by(canvas_id: local_canvas_id)
        return course.name if course
      end
    else
      course = Course.find_by(canvas_id: canvas_id)
      return course.name if course
    end

    nil
  end
end
