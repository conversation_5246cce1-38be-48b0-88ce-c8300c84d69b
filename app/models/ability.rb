# frozen_string_literal: true

class Ability
  include CanCan::Ability
  include PandaPal::Concerns::AbilityHelper
  include CanvasSync::Concerns::AbilityHelper

  def initialize(user, rails_session:, panda_session:)
    @user = user
    @panda_pal_session = panda_session
    @rails_session = rails_session

    if user_is_account_admin?
      can :launch_from, :account
    elsif user_is_course_admin?
      can :launch_from, :course
    end

    return unless user_is_account_admin? || user_is_course_admin?

    can :manage, StudentObserverLink
    can :read, User
  end

  def user_is_account_admin?
    true
  end

  def user_is_course_admin?
    @user_is_course_admin ||= launch_context.is_a?(Course) && Enrollment.active.course_admin.where(canvas_user_id: @user.canvas_id, course: launch_context).exists?
  end
end
