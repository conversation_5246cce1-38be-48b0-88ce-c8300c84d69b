# View-Specific Calendar Pickers Implementation

## Overview
Implemented view-specific calendar pickers for all calendar views (Month, Week, and Agenda) with each view having its own unique picker behavior:

- **Week View**: Shows week range picker (e.g., "4 Sep 2025 – 10 Sep 2025")
- **Month View**: Shows month range picker (e.g., "1 Sep 2025 – 30 Sep 2025")
- **Agenda View**: Shows single start date picker (e.g., "4 Sep 2025") since agenda shows all events from selected date forward

## Changes Made

### 1. CalendarLayout.jsx Updates

#### New State Variables Added:
- `selectedAgendaDate` - Tracks the selected start date for agenda view
- Enhanced `selectedWeekDate` - Already existed, now properly integrated
- `showDateSelector` - Controls visibility of date input for all views

#### New Functions Added:
- `formatMonthRange()` - Calculates and formats month range for month view
- `formatAgendaDate()` - Formats single date for agenda view
- Enhanced `formatWeekRange()` - Already existed, now properly integrated
- Updated `handleToday()` - Now resets all view-specific dates to today
- Updated `handlePrevMonth()` - Now works view-specifically (prev week for week view, prev month for month/agenda)
- Updated `handleNextMonth()` - Now works view-specifically (next week for week view, next month for month/agenda)

#### View-Specific Date Logic:
- **Month View**: Shows full month range (e.g., "1 Sep 2025 – 30 Sep 2025")
- **Week View**: Shows week range (e.g., "4 Sep 2025 – 10 Sep 2025")
- **Agenda View**: Shows single start date (e.g., "4 Sep 2025")

#### UI Changes:
- **Month View**: Clickable month range link that opens month date picker
- **Week View**: Clickable week range link that opens week date picker
- **Agenda View**: Clickable start date link that opens single date picker
- Each view has its own DateInput component with appropriate labels
- Consistent interaction pattern across all views

### 2. Enhanced Navigation:
- **Prev/Next Buttons**: Now work view-specifically
  - Month view: Navigate by month
  - Week view: Navigate by week (7 days)
  - Agenda view: Navigate by month
- **Today Button**: Resets all views to current date
- **Auto-sync**: displayedDate automatically syncs with view-specific dates

### 3. Enhanced Loading States:
- **MonthView**: Added proper loading message (was previously incomplete)
- **AgendaView**: Already had loading state (maintained)
- **WeekView**: Already had loading state (maintained)
- Consistent loading experience across all views

### 4. Fixed Agenda Date Filtering:
- **AgendaView**: Now respects `selectedAgendaDate` prop
- **Event Filtering**: Shows only events from selected date forward
- **Date Integration**: Properly syncs with date picker selection

### 5. Cleaned Up Code:
- Removed unused imports (`Text` component)
- Removed unused variables (`monthYearLabel`)
- Maintained existing `formatWeekRange` function

## Features

### View-Specific Clickable Labels
- **Month View**: Shows month range label (e.g., "1 Sep 2025 – 30 Sep 2025")
- **Week View**: Shows week range label (e.g., "4 Sep 2025 – 10 Sep 2025")
- **Agenda View**: Shows single start date label (e.g., "4 Sep 2025")
- Clicking any label opens the appropriate date input field

### View-Specific Date Inputs
- **Week View**: Selecting any date navigates to the week containing that date
- **Month View**: Selecting any date navigates to that month
- **Agenda View**: Selecting any date sets it as the start date for showing events from that date forward

### Consistent Behavior
- Same interaction pattern across all views
- Unified styling and placement
- Automatic date input closure after selection

### Navigation Behavior

#### Prev/Next Buttons:
- **Month View**: Navigate by month (e.g., Sep → Oct)
- **Week View**: Navigate by week (e.g., Sep 4-10 → Sep 11-17)
- **Agenda View**: Navigate by month (affects start date)

#### Today Button:
- Resets all views to current date
- Month view: Goes to current month
- Week view: Goes to current week
- Agenda view: Sets start date to today

## Technical Implementation

### Month Range Calculation
```javascript
const formatMonthRange = useMemo(() => {
  const formatDate = (dateStr) => {
    const date = new Date(dateStr)
    return date.toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    })
  }
  const monthStart = `${meta.year}-${String(meta.month + 1).padStart(2, '0')}-01`
  const monthEnd = `${meta.year}-${String(meta.month + 1).padStart(2, '0')}-${new Date(meta.year, meta.month + 1, 0).getDate()}`
  return `${formatDate(monthStart)} – ${formatDate(monthEnd)}`
}, [meta])
```

### Week Range Calculation
```javascript
const formatWeekRange = useMemo(() => {
  const formatDate = (dateStr) => {
    const date = new Date(dateStr)
    return date.toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    })
  }
  return `${formatDate(weekStart)} – ${formatDate(weekEnd)}`
}, [weekStart, weekEnd])
```

### Agenda Date Formatting
```javascript
const formatAgendaDate = useMemo(() => {
  return selectedAgendaDate.toLocaleDateString('en-GB', {
    day: 'numeric',
    month: 'short',
    year: 'numeric'
  })
}, [selectedAgendaDate])
```

## User Experience

### Before
- Month view: Static text showing "Month Year"
- Week view: Clickable link showing week range
- Agenda view: No date selector

### After
- All views: Clickable date range label showing appropriate range
- Consistent interaction: click to open date picker
- Unified styling and behavior

## Testing

### Manual Testing Steps
1. **Month View**:
   - Verify date range shows full month (e.g., "1 Sep 2025 – 30 Sep 2025")
   - Click label to open date input
   - Select different date, verify navigation to that month
   - Test loading state when events are being fetched

2. **Week View**:
   - Verify date range shows week (e.g., "4 Sep 2025 – 10 Sep 2025")
   - Click label to open date input
   - Select different date, verify navigation to week containing that date
   - Test prev/next buttons navigate by week
   - Test loading state when events are being fetched

3. **Agenda View**:
   - Verify single start date (e.g., "4 Sep 2025")
   - Click label to open date input
   - Select different date, verify only events from that date forward are shown
   - Test prev/next buttons navigate by month (affecting start date)
   - Test loading state when events are being fetched

4. **Navigation Testing**:
   - Test Today button resets all views to current date
   - Test prev/next buttons work contextually for each view
   - Test view switching maintains appropriate dates

### Build Verification
- ✅ Webpack build successful with no errors
- ✅ No JavaScript syntax errors
- ✅ All imports and dependencies resolved correctly

## Files Modified
- `app/javascript/components/shared/calendar/CalendarLayout.jsx`

## Dependencies
- Uses existing `@instructure/ui-date-input` component
- No new dependencies added
- Maintains compatibility with existing calendar functionality

## Future Enhancements
- Could add keyboard shortcuts for date navigation
- Could add preset date range buttons (Today, This Week, This Month)
- Could add date range validation to prevent invalid selections
